import { TextInput } from "../form-fields";
import { formatPhone } from "../utils/formatting";
import { ValidationErrors, FormData } from "../utils/validation";

interface ContactSectionProps {
  formData: FormData;
  errors: ValidationErrors;
  onChange: (field: string, value: string) => void;
}

export const ContactSection = ({ formData, errors, onChange }: ContactSectionProps) => {
  return (
    <div className="mb-10">
      <h2 className="text-lg font-medium text-gray-900 mb-6">Contact Information</h2>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <TextInput
          label="Business Email"
          value={formData.email || ""}
          onChange={(value) => onChange("email", value)}
          error={errors.email}
          required
          type="email"
          placeholder="<EMAIL>"
        />

        <TextInput
          label="Business Phone"
          value={formatPhone(formData.phone || "")}
          onChange={(value) => onChange("phone", value.replace(/\D/g, ""))}
          error={errors.phone}
          required
          type="tel"
          placeholder="(*************"
          maxLength={14}
        />
      </div>
    </div>
  );
};