import { BUSINESS_TYPE_CHECKS } from "../constants/businessConstants";

export interface ValidationErrors {
  [key: string]: string;
}

export interface FormData {
  name?: string;
  type?: number;
  ein?: string;
  website?: string;
  email?: string;
  phone?: string;
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  zip?: string;
  country?: string;
  public?: number;
  merchant?: {
    dba?: string;
    new?: number;
    mcc?: string;
    status?: string;
    established?: string;
    avgTicket?: number;
    annualCCSales?: number;
    members?: Array<{
      title?: string;
      first?: string;
      middle?: string;
      last?: string;
      ssn?: string;
      dob?: string;
      dl?: string;
      dlstate?: string;
      ownership?: number;
      significantResponsibility?: number;
      politicallyExposed?: number;
      email?: string;
      phone?: string;
      primary?: string;
      address1?: string;
      address2?: string;
      city?: string;
      state?: string;
      zip?: string;
      country?: string;
    }>;
  };
}

export const validateBusinessInfoForm = (formData: FormData): ValidationErrors => {
  const errors: ValidationErrors = {};
  const { requiresCorporateStructure, isSoleProprietor } = BUSINESS_TYPE_CHECKS;
  
  if (!formData.name?.trim()) errors.name = "Legal business name is required";
  if (!formData.type) errors.type = "Business type is required";
  if (!formData.ein?.trim()) {
    errors.ein = formData.type && isSoleProprietor(formData.type) 
      ? "Tax ID/EIN or SSN is required" 
      : "Tax ID/EIN is required";
  }
  if (!formData.website?.trim()) errors.website = "Website is required";
  if (!formData.email?.trim()) errors.email = "Business email is required";
  if (!formData.phone?.trim()) errors.phone = "Business phone is required";
  if (!formData.address1?.trim()) {
    errors.address1 = "Address is required";
  } else if (/^(p\.?\s?o\.?\s?box|post\s?office\s?box)/i.test(formData.address1)) {
    errors.address1 = "PO Boxes are not acceptable for business address";
  }
  if (!formData.city?.trim()) errors.city = "City is required";
  if (!formData.state?.trim()) errors.state = "State is required";
  if (!formData.zip?.trim()) errors.zip = "ZIP code is required";
  if (!formData.merchant?.mcc?.trim()) errors.mcc = "MCC code is required";
  if (!formData.merchant?.established?.trim()) errors.established = "Business establishment date is required";
  if (formData.merchant?.annualCCSales === undefined || formData.merchant?.annualCCSales === null || formData.merchant?.annualCCSales === 0) {
    errors.annualCCSales = "Annual sales is required";
  }

  if (formData.type && requiresCorporateStructure(formData.type) && !formData.merchant?.dba?.trim()) {
    errors.dba = "DBA/Statement descriptor is required for this business type";
  }

  if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
    errors.email = "Please enter a valid email address";
  }

  if (formData.phone && !/^\d{10}$/.test(formData.phone.replace(/\D/g, ""))) {
    errors.phone = "Phone must be 10 digits";
  }

  if (formData.ein && !/^\d{9}$/.test(formData.ein.replace(/\D/g, ""))) {
    errors.ein = "EIN must be 9 digits";
  }

  if (formData.zip && !/^\d{5}(-\d{4})?$/.test(formData.zip)) {
    errors.zip = "ZIP code must be 5 digits or 5-4 format";
  }

  if (formData.merchant?.mcc && !/^\d{4}$/.test(formData.merchant.mcc)) {
    errors.mcc = "MCC must be 4 digits";
  }

  return errors;
};