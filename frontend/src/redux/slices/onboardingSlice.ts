import { createSlice, type PayloadAction } from "@reduxjs/toolkit";
import { type CreatePayrixMerchantRequest } from "../../services/api.ts";

type FormData = Partial<CreatePayrixMerchantRequest>;

interface OnboardingState {
  step: number;
  formData: FormData;
  status: "idle" | "loading" | "succeeded" | "failed";
  error: string | null;
}

const initialState: OnboardingState = {
  step: 1,
  formData: {},
  status: "idle",
  error: null,
};

// Demo data that matches the expected schema
export const DEMO_MERCHANT_DATA: CreatePayrixMerchantRequest = {
  // Business Information
  name: "Tech Solutions Inc",
  dba: "TechSol",
  ein: "*********",
  type: 2, // Corporation
  public: 0, // Private
  website: "https://techsolutions.com",
  email: "<EMAIL>",
  address1: "123 Main Street",
  address2: "Suite 100",
  city: "San Francisco",
  state: "CA",
  zip: "94102",
  country: "USA",
  phone: "4155551234",
  customerPhone: "4155551234",
  mcc: "5734", // Computer Software Stores
  status: 1, // Ready
  tcVersion: "1.0",
  tcDate: (() => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, "0");
    const day = String(now.getDate()).padStart(2, "0");
    const hour = String(now.getHours()).padStart(2, "0");
    const minute = String(now.getMinutes()).padStart(2, "0");
    return `${year}${month}${day}${hour}${minute}`;
  })(), // Current date in YYYYMMDDHHMM format
  clientIp: "***********",
  currency: "USD",

  // Payment Parameters
  annualCCSales: 0,
  avgTicket: 150,
  established: "********",

  // Bank Account Information
  accounts: [
    {
      primary: 1,
      currency: "USD",
      account: {
        method: 10, // Corporate checking
        number: "**********",
        routing: "*********",
      },
    },
  ],

  // Primary Owner Information
  merchant: {
    dba: "TechSol",
    new: 1,
    mcc: "5734",
    status: "1",
    annualCCSales: 0,
    avgTicket: 150,
    established: "********",
    members: [
      {
        title: "CEO",
        first: "sam",
        middle: "A",
        last: "park",
        ssn: "*********",
        dob: "********", // YYYYMMDD format
        dl: "D*********",
        dlstate: "CA",
        ownership: 10000, // 100% in basis points
        significantResponsibility: 1,
        politicallyExposed: 0,
        email: "<EMAIL>",
        phone: "**********",
        primary: "1",
        address1: "123 Main Street",
        address2: "Suite 100",
        city: "San Francisco",
        state: "CA",
        zip: "94102",
        country: "USA",
      },
    ],
  },

  // User account creation fields (mandatory)
  createAccount: true,
  username: "",
  password: "",
  confirmPassword: "",
};

const onboardingSlice = createSlice({
  name: "onboarding",
  initialState,
  reducers: {
    nextStep: (state) => {
      state.step += 1;
    },
    prevStep: (state) => {
      state.step -= 1;
    },
    goToStep: (state, action: PayloadAction<number>) => {
      state.step = action.payload;
    },
    updateFormData: (state, action: PayloadAction<Partial<FormData>>) => {
      state.formData = { ...state.formData, ...action.payload };
    },
    fillDemoData: (state) => {
      state.formData = { ...DEMO_MERCHANT_DATA };
    },
    resetOnboarding: () => initialState,
  },
});

export const { nextStep, prevStep, goToStep, updateFormData, fillDemoData, resetOnboarding } = onboardingSlice.actions;
export default onboardingSlice.reducer;
